# SendMessage函数优化文档

## 优化概述

对`FShaderCompileBKDistThreadRunnable::SendMessage`函数进行了全面优化，以正确处理HTTP响应，特别是支持HTTP chunked传输编码。

## 主要改进

### 1. 完整的HTTP响应处理
- **原始问题**: 只接收一次数据，无法处理大型响应或chunked传输
- **优化方案**: 实现完整的HTTP协议解析，支持多种传输方式

### 2. 支持HTTP Chunked传输编码
- **新增功能**: 自动检测和解析`Transfer-Encoding: chunked`
- **实现方法**: 
  - 解析chunk大小（十六进制）
  - 逐个读取chunk数据
  - 正确处理chunk结束标记（0\r\n\r\n）

### 3. 支持Content-Length传输
- **功能**: 根据Content-Length头部精确接收指定长度的数据
- **优势**: 避免数据截断或不完整接收

### 4. 增强的错误处理和超时机制
- **套接字超时**: 设置接收和发送超时（30秒接收，10秒发送）
- **详细错误码**: 提供具体的错误信息和错误码
- **连接状态检查**: 检测连接断开和异常情况

### 5. 改进的数据发送机制
- **完整发送**: 确保所有数据都被发送完毕
- **发送进度跟踪**: 记录已发送字节数
- **发送失败处理**: 详细的发送失败日志

## 新增方法

### ReceiveHttpResponse
```cpp
int32 ReceiveHttpResponse(FSocket* Socket, FString& OutResponse);
```
- **功能**: 完整接收HTTP响应
- **支持**: Chunked编码、Content-Length、连接关闭检测
- **返回值**: 0表示成功，负数表示各种错误类型

### ParseHttpChunkedData
```cpp
int32 ParseHttpChunkedData(const TArray<uint8>& ChunkedData, TArray<uint8>& OutDecodedData);
```
- **功能**: 解析HTTP chunked传输编码的数据
- **处理**: 十六进制chunk大小、chunk扩展、结束标记
- **输出**: 解码后的完整数据

## HTTP协议支持

### 1. 响应头解析
- 自动检测`Transfer-Encoding: chunked`
- 解析`Content-Length`头部
- 支持大小写不敏感的头部匹配

### 2. Chunked传输编码
```
chunk-size [ chunk-extension ] CRLF
chunk-data CRLF
...
0 CRLF
[ trailer ] CRLF
```
- 正确解析十六进制chunk大小
- 处理可选的chunk扩展
- 检测结束chunk（大小为0）

### 3. 数据完整性
- 确保接收完整的HTTP响应
- 验证chunk数据的完整性
- 处理连接异常和超时

## 错误处理

### 错误码定义
- `-1`: 无效套接字
- `-2`: 接收HTTP头部失败
- `-3`: 接收头部时连接关闭
- `-4`: Chunked数据解析失败
- `-5`: 接收HTTP主体失败
- `-6`: 接收主体时连接关闭

### 日志输出
- **详细日志**: 记录接收过程的详细信息
- **错误诊断**: 提供具体的错误原因
- **性能监控**: 记录数据传输量和处理时间

## 性能优化

### 1. 缓冲区管理
- 动态缓冲区大小调整
- 减少内存拷贝操作
- 高效的数据追加机制

### 2. 网络优化
- 设置合适的套接字超时
- 批量数据发送
- 连接状态监控

### 3. 内存效率
- 及时释放临时缓冲区
- 避免不必要的字符串转换
- 优化大数据处理

## 兼容性

### 向后兼容
- 保持原有的函数签名
- 支持原有的调用方式
- 不影响现有功能

### HTTP标准兼容
- 符合RFC 7230 HTTP/1.1规范
- 正确处理CRLF行结束符
- 支持标准的传输编码

## 使用示例

```cpp
FString response;
int result = SendMessage(TEXT("127.0.0.1"), 8080, TEXT("{}"), TEXT("/api/test"), &response);
if (result == 0)
{
    // 成功接收完整响应
    UE_LOG(LogShaderCompilers, Display, TEXT("Received: %s"), *response);
}
else
{
    // 处理错误
    UE_LOG(LogShaderCompilers, Error, TEXT("HTTP request failed with code: %d"), result);
}
```

## 测试建议

1. **Chunked传输测试**: 使用返回chunked数据的服务器进行测试
2. **大数据传输测试**: 测试大型JSON响应的接收
3. **网络异常测试**: 模拟网络中断和超时情况
4. **并发测试**: 验证多个并发请求的处理能力

## 总结

优化后的SendMessage函数提供了：
- 完整的HTTP协议支持
- 可靠的数据传输机制
- 详细的错误处理和日志
- 良好的性能和内存效率
- 向后兼容性

这些改进确保了BK分布式着色器编译器能够可靠地与HTTP服务进行通信，特别是在处理大型响应和chunked传输时。
