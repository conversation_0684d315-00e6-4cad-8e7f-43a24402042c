# ResolveAvailableResponse函数优化文档

## 优化概述

对`FShaderCompileBKDistThreadRunnable::ResolveAvailableResponse`函数进行了全面优化，以正确解析JSON响应中的`failed_actions`字段，并根据失败的action信息将对应的批次移动到本地重试队列中。

## 主要改进

### 1. 正确解析failed_actions数组
- **原始问题**: 只是简单地将failed_actions作为字符串输出，没有进行实际处理
- **优化方案**: 正确解析JSON数组，提取每个失败action的详细信息

### 2. 智能批次匹配算法
- **多重匹配策略**: 实现了三种匹配方式确保准确找到失败的批次
- **文件大小验证**: 使用文件大小作为额外的验证条件

### 3. 自动本地重试机制
- **失败检测**: 自动检测BK分布式编译失败的任务
- **队列转移**: 将失败的批次从执行队列移动到本地重试队列

## JSON数据结构支持

### 输入JSON格式
```json
{
  "result": true,
  "code": 0,
  "message": "request OK",
  "data": {
    "pid": 27796,
    "failed_actions": [
      {
        "index": 173,
        "errormsg": "",
        "exitcode": -1,
        "process_id": 15012,
        "input_file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealShaderWorkingDir\\7F3B127C4A00F4CC88630CA362926925\\BKDist\\40B810D74B1B2D5D2166FA92692D94EA_0_174_Worker.in",
        "input_file_size": 68391
      }
    ]
  }
}
```

### 解析的字段
- `index`: Action索引
- `input_file`: 输入文件完整路径
- `input_file_size`: 输入文件大小（字节）
- `exitcode`: 退出码
- `errormsg`: 错误消息
- `process_id`: 进程ID

## 新增方法

### ProcessFailedActions
```cpp
void ProcessFailedActions(const TArray<TSharedPtr<FJsonValue>>& FailedActions);
```
- **功能**: 处理失败的action数组
- **流程**: 
  1. 解析每个失败action的详细信息
  2. 根据输入文件路径和大小查找对应的批次
  3. 将找到的批次移动到本地重试队列

### FindBatchByInputFile
```cpp
FShaderBatch* FindBatchByInputFile(const FString& InputFilePath, int64 InputFileSize);
```
- **功能**: 根据输入文件路径和大小查找对应的批次
- **匹配策略**:
  1. **精确路径匹配**: 完整路径匹配 + 文件大小验证
  2. **文件名匹配**: 文件名匹配 + 文件大小验证
  3. **索引匹配**: 从文件名提取批次索引进行匹配

## 批次匹配算法

### 1. 精确路径匹配
- 比较完整的文件路径（忽略大小写）
- 验证文件大小是否匹配
- 优先级最高，准确性最好

### 2. 文件名匹配
- 提取文件名进行比较
- 适用于路径格式不同但文件名相同的情况
- 必须配合文件大小验证

### 3. 批次索引匹配
- 使用正则表达式从文件名提取批次索引
- 匹配模式: `_(\\d+)_Worker\\.in$`
- 根据BatchIndex进行匹配
- 适用于文件名格式标准化的情况

## 工作流程

<augment_code_snippet path="Engine/Source/Runtime/Engine/Private/ShaderCompiler/ShaderCompilerBK.cpp" mode="EXCERPT">
```cpp
void FShaderCompileBKDistThreadRunnable::ProcessFailedActions(const TArray<TSharedPtr<FJsonValue>>& FailedActions)
{
    // 1. 解析失败action信息
    for (const auto& ActionValue : FailedActions)
    {
        // 提取index, input_file, input_file_size, exitcode, errormsg
        
        // 2. 查找对应的批次
        FShaderBatch* FailedBatch = FindBatchByInputFile(InputFile, InputFileSize);
        
        // 3. 标记为需要本地重试
        if (FailedBatch)
        {
            BatchesToMoveToRetry.AddUnique(FailedBatch);
        }
    }
    
    // 4. 移动批次到本地重试队列
    for (FShaderBatch* Batch : BatchesToMoveToRetry)
    {
        ShaderBatchesInFlight.Remove(Batch);
        ShaderBatchesFailedForLocalRetry.Add(Batch);
    }
}
```
</augment_code_snippet>

## 错误处理和日志

### 详细日志输出
- **失败action信息**: 记录每个失败action的详细信息
- **匹配过程**: 记录批次匹配的过程和结果
- **队列操作**: 记录批次在队列间的移动

### 错误处理
- **无效JSON对象**: 跳过无效的action对象
- **批次未找到**: 记录警告但继续处理其他action
- **文件大小不匹配**: 记录详细的不匹配信息

## 性能优化

### 1. 高效查找
- 使用AddUnique避免重复添加批次
- 优先使用精确匹配减少计算开销

### 2. 内存管理
- 及时移动批次避免内存泄漏
- 使用引用传递减少拷贝开销

### 3. 正则表达式优化
- 只在前两种匹配失败时才使用正则表达式
- 缓存正则表达式模式

## 兼容性和可靠性

### 向后兼容
- 保持原有的PID检测功能
- 不影响正常的BK分布式编译流程

### 容错机制
- 处理不完整的failed_actions数据
- 支持部分字段缺失的情况
- 文件大小为0时仍能进行基本匹配

## 使用场景

### 1. BK分布式编译失败
- 网络问题导致的编译失败
- 远程节点资源不足
- 编译器版本不兼容

### 2. 部分任务失败
- 只有部分着色器编译失败
- 需要精确识别失败的批次
- 避免重新编译成功的任务

### 3. 自动故障恢复
- 无需人工干预
- 自动切换到本地编译
- 保证编译流程的连续性

## 测试建议

1. **模拟失败场景**: 构造包含failed_actions的JSON响应
2. **文件匹配测试**: 验证不同路径格式的文件匹配
3. **大小验证测试**: 测试文件大小不匹配的处理
4. **批量失败测试**: 测试多个action同时失败的情况
5. **边界条件测试**: 测试空数组、无效JSON等边界情况

## 总结

优化后的ResolveAvailableResponse函数提供了：
- 完整的failed_actions解析支持
- 智能的批次匹配算法
- 自动的本地重试机制
- 详细的错误处理和日志
- 高效的性能和内存管理

这些改进确保了BK分布式着色器编译器能够智能地处理编译失败，自动将失败的任务转移到本地重试，大大提高了系统的可靠性和用户体验。
